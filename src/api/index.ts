import HttpClient from '../libs/axios'
import md5 from 'md5'
import { AxiosInstance } from 'axios'
import config from '../config'
const baseUrl = process.env.NODE_ENV === 'development' ? config.publicPath.dev : config.publicPath.pro

const userUrl = 'api/users'
const buildingUrl = 'api/buildings'
const subscriptionGroupUrl = 'api/subscriptiongroups'
const userByAdminUrl = 'api/usersbyadmin'
const profileUrl = 'api/profile'
const authUrl = 'api/authentication'
const forgotUrl = 'api/forgot'
const uploadUrl = 'api/upload'
const fileUrl = 'api/files'
const ticketUrl = 'api/tickets'
const financeTicketUrl = 'api/financeticketings'
const ticketlogUrl = 'api/ticketlogs'
const ticketSummaryUrl = 'api/stats'
const planUrl = 'api/plans'
const planDmaUrl = 'api/dmaplans'
const userDmaUrl = 'api/dmacheckuser'
const usercheck1 = 'api/checkuser'
const paymentUrl = 'api/payments'
const subscriptionUrl = 'api/subscriptions'
const billingUrl = 'api/bills'
const prebillsUrl = 'api/prebills'
const prebillitemUrl = 'api/prebillitems'
const billSummaryUrl = 'api/reports/billsummary'
const zeroSpecialRateReportUrl = 'api/reports/zerospecialratelist'
const dashboardSummaryUrl = 'api/dashboardsummary'
const prebillingPaymentBreakdownUrl = 'api/reports/prebillingspaymentbreakdown'
const billingsPaymentBreakdownUrl = 'api/reports/billingspaymentbreakdown'
const invalidIcUserReportUrl = 'api/reports/invalidicuserlist'
const orphanDMAUserReportUrl = 'api/reports/orphandmauserlist'

const subscriptionLogsUrl = 'api/subscriptionlogs'

class API extends HttpClient {

  protected instance : AxiosInstance

  public constructor() {
    super(baseUrl)
    this.instance = super.getInstance()
  }

  public getIPay256Hash = (data: any) => {
    return this.instance.request({
      url: 'api/ipayhash',
      method: 'post',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      data: data.item
    })
  }

  public getDashboardSummary = (data: any) => {
    return this.instance.request({
      url: dashboardSummaryUrl,
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }

  public getPrebillingPaymentBreakdown = (data: any) => {
    return this.instance.request({
      url: prebillingPaymentBreakdownUrl + '/' + data.startdate + '/' + data.enddate,
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }

  public getSubscriberCount = (data: any) => {
    return this.instance.request({
      url: 'api/subscribercount?building=' + encodeURIComponent(data.building),
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }

  public getBillingsPaymentBreakdown = (data: any) => {
    return this.instance.request({
      url: billingsPaymentBreakdownUrl + '/' + data.startdate + '/' + data.enddate,
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }

  public getInvalidIcUserList = (data: any) => {
    return this.instance.request({
      url: invalidIcUserReportUrl,
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }

  public getBillingLogs = (data: any) => {
    return this.instance.request({
      url: 'api/billinglogs/',
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }

  public getPrebillLogs = (data: any) => {
    return this.instance.request({
      url: 'api/prebilllogs/',
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }

  public getPlanLogs = (data: any) => {
    return this.instance.request({
      url: 'api/planlogs/',
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }


  public getOrphanDMAUserList = (data: any) => {
    return this.instance.request({
      url: orphanDMAUserReportUrl,
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }

  public getStatementOfAccount = (data: any) => {
    return this.instance.request({
      url: 'api/reports/statementofaccount/' + data.subscription,
      method: 'post',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      data: data.form,
      responseType: 'blob'
    })
  }

  public getSubscriptionLogs = (data: any) => {
    return this.instance.request({
      url: subscriptionLogsUrl + '/' + data.id,
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }

  public recalculatePayment = (data: any) => {
    return this.instance.request({
      url: 'api/recalculatepayment/' + data.id,
      method: 'get',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
    })
  }

  public register = (data: any) => {
    if (data.password) {
      data.password = md5(data.password)
    }
    return this.instance.request({
      url: userUrl,
      data,
      method: 'post'
    })
  }

  public createSubplacePayment = (data: any) => {
    return this.instance.request({
      url: 'api/subplace/payment/' + data.id + '/' + data.method,
      method: 'get'
    })
  }

  public getProfile = (data: any) => {
    // console.log('get Profile')
    return this.instance.request({
      url: profileUrl,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getClientBills = (data: any) => {
    return this.instance.request({
      url: 'api/mybills',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getDMAUserCheck = (data: any) => {
    return this.instance.request({
      url: userDmaUrl + "/" + data.username,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getUserByUsername = (data: any) => {
    return this.instance.request({
      url: usercheck1 + '/' + data.username,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getUserById = (data: any) => {
    return this.instance.request({
      url: 'api/usernamebyid/' + data.id,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public createDMAUser = (data: any) => {
    return this.instance.request({
      url: 'api/dmauser/' + data.id,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'post'
    })
  }

  public updateUserDMAPlan = (data: any) => {
    return this.instance.request({
      url: 'api/updatedmaplan/' + data.id,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'post'
    })
  }

  public login = (data: any) => {
    if (data.password) {
      data.password = md5(data.password)
    }
    // console.log(data)
    return this.instance.request({
      url: authUrl,
      data,
      method: 'post'
    })
  }

  public forgot = (email: any) => {
    return this.instance.request({
      url: forgotUrl,
      data: email,
      method: 'post'
    })
  }

  public resetPasswordNow = (data: any) => {
    if (data.password) {
      data.password = md5(data.password)
    }
    return this.instance.request({
      url: 'api/resetpassnow',
      data: data,
      method: 'post'
    })
  }

  public logout = (token: string) => {
    return this.instance.request({
      url: 'api/logout',
      headers: {
        Authorization: 'Bearer ' + token
      },
      method: 'delete'
    })
  }

  public updateProfile = (data: any) => {
    return this.instance.request({
      url: 'api/profile',
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      data: data.form,
      method: 'patch'
    })
  }

  public getUnbillReport = (data: any) => {
    return this.instance.request({
      url: 'api/reports/unbills/' + data.days,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getBillingReport = (data: any) => {
    return this.instance.request({
      url: 'api/reports/billings/' + data.startdate + '/' + data.enddate,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getBillingByPlanReport = (data: any) => {
    return this.instance.request({
      url: 'api/reports/billsummarybyplanname/' + data.type + '/' + data.startdate + '/' + data.enddate,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getBillingWtBuildingReport = (data: any) => {
    return this.instance.request({
      url: 'api/reports/billingswtbuilding/' + data.startdate + '/' + data.enddate,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getTicketReport = (data: any) => {
    return this.instance.request({
      url: 'api/reports/ticketsummary?startdate=' + data.startdate + '&enddate=' + data.enddate,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getOpenTicketReport = (data: any) => {
    return this.instance.request({
      url: 'api/reports/openticketsummary/' + data.startdate + '/' + data.enddate,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getUpcomingBillableReport = (data: any) => {
    const daysremain = data.daysremain ? data.daysremain : 31
    return this.instance.request({
      url: 'api/reports/upcomingbillable?daysremain=' + daysremain,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getBillSummary = (data: any) => {
    return this.instance.request({
      url: billSummaryUrl + '/' + data.startdate + '/' + data.enddate,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getUserName = (data: any) => {
    return this.instance.request({
      url: 'api/usernamebyid/' + data.id,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public searchUser = (data: any) => {
    let limit = data.limit || 5
    let dll = ""
    if (data.params) {
      data.params.forEach((g: string) => {
        if (data[g]!=null) {
          dll += g + '=' + data[g] + '&'
        }
      })
    }
    return this.instance.request({
      url: 'api/searchusers?$keywords=' + data.keywords + '&$limit=' + limit + '&' + dll,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getItemFunc = (purl: string, data: any) => {
    let headers = {}
    if (data.token) {
      headers = {
        Authorization: 'Bearer ' + data.token
      }
    }
    return this.instance.request({
      url: `${purl}/${data.id}` ,
      headers,
      method: 'get'
    })
  }

  public getItemsFunc = (purl: string, data: any) => {
    let dll = ''
    let skip = 0
    let limit = 10

    if (data.limit) {
      limit = Number.parseInt(data.limit)
    }
    if (data.skip) {
      skip = Number.parseInt(data.skip)
    } else {
      if (data.page) {
        const page = Number.parseInt(data.page)
        skip = (page - 1) * limit
        if (skip < 0) {
          skip = 0
        }
      }
    }
    dll += '$skip=' + skip + '&'

    dll += '$limit=' + limit + '&'
    if (data.keywords && data.keywords.trim().length > 0) {
      let keywords = data.keywords.trim()
      dll += '$keywords=' + keywords + '&'
    }

    if (data.params) {
      data.params.forEach((g: string) => {
        if (data[g]!=null) {
          dll += g + '=' + data[g] + '&'
        }
      })
    }

    for (let i = 0; i < Object.keys(data).length; i++) {
      let k = Object.keys(data)[i]
      if (k !== 'limit' && k !== 'skip' && k !== 'page' && k !== 'params' && k !== 'token' && k !== 'keywords') {
        dll +=  k + '=' + data[k] + '&'
      }
    }

    return this.instance.request({
      url: `${purl}?${dll}`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public deleteItemFunc = (purl:string, data: any) => {
    return this.instance.request({
      url: `${purl}/${data.id}` ,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'delete'
    })
  }

  public createItemFunc = (purl: string, data: any) => {
    return this.instance.request({
      url: `${purl}` ,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      data: data.form,
      method: 'post'
    })
  }

  public updateItemFunc = (purl: string, data: any) => {
    return this.instance.request({
      url: `${purl}/${data.id}` ,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      data: data.form,
      method: 'patch'
    })
  }

  public patchItemFunc = (purl: string, data: any) => {
    return this.instance.request({
      url: `${purl}/${data.id}?$openticket=1` ,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      data: {},
      method: 'patch'
    })
  }

  public updateUserFunc = (data: any) => {
    if (data.form.password && data.form.password.trim().length > 0) {
      data.form.password = md5(data.form.password)
    } else {
      delete data.form.password
    }
    return this.instance.request({
      url: `api/users/${data.id}` ,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      data: data.form,
      method: 'patch'
    })
  }

  public sendBill = (data: any) => {
    return this.instance.request({
      url: `api/printbill/${data.data.item.id}.pdf`,
      data: data.data,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'post'
    })
  }

  public enableUser = (data: any) => {
    return this.instance.request({
      url: `api/enableuser/${data.data.id}`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'post'
    })
  }

  public getNextSupportID = (data: any) => {
    return this.instance.request({
      url: `api/nextconceptids/support`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public pushSupportID = (data: any) => {
    return this.instance.request({
      url: `api/pushconceptids/support`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getNextSubscriptionID = (data: any) => {
    return this.instance.request({
      url: `api/nextconceptids/subscription`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public pushSubscriptionID = (data: any) => {
    return this.instance.request({
      url: `api/pushconceptids/subscription`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public generatebillspecific = (data: any) => {
    return this.instance.request({
      url: `api/generatebillspecific2/${data.id}/${data.date}`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public billMigrateToCustomer = (data: any) => {
    return this.instance.request({
      url: `https://paymentapi.highfi.com.my/api/billmigratetocustomer/${data.subscriptionId}`,
      headers: {
        Authorization: 'bearer ' + data.token,
        'Content-Type': 'application/json'
      },
      method: 'post',
      data: {
        customerid: data.customerId
      }
    })
  }

  public searchPaymentSubscriptions = (data: any) => {
    return this.instance.request({
      url: `https://paymentapi.highfi.com.my/api/subscriptions?$skip=${data.skip || 0}&$limit=${data.limit || 10}&$keywords=${encodeURIComponent(data.keywords || '')}`,
      headers: {
        Authorization: 'bearer ' + data.token
      },
      method: 'get'
    })
  }

  public getPaymentBills = (data: any) => {
    return this.instance.request({
      url: `https://paymentapi.highfi.com.my/api/bills?$skip=${data.skip || 0}&$limit=${data.limit || 50}&subscriptions=${data.subscriptionId}`,
      headers: {
        Authorization: 'bearer ' + data.token
      },
      method: 'get'
    })
  }

  public updatePaymentSubscription = (data: any) => {
    return this.instance.request({
      url: `https://paymentapi.highfi.com.my/api/subscriptions/${data.subscriptionId}`,
      headers: {
        Authorization: 'bearer ' + data.token,
        'Content-Type': 'application/json'
      },
      method: 'patch',
      data: data.updateData
    })
  }

  public disconnectUser = (data: any) => {
    return this.instance.request({
      url: `api/disconnectuser/${data.data.id}`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'post'
    })
  }

  public disableUser = (data: any) => {
    return this.instance.request({
      url: `api/disableuser/${data.data.id}`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'post'
    })
  }

  public createReturnUser = (data: any) => {
    return this.instance.request({
      url: `api/userscreatereturn`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      data: data.form,
      method: 'post'
    })
  }

  public updateUserPlan = (data: any) => {
    return this.instance.request({
      url: `api/updateuserplan`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      data: data.form,
      /**
       * {
          "username": "bb_l12_u17_536@highfi",
          "plan": "34"
        }
       */
      method: 'post'
    })
  }

  public getSubscriptionsEmails= (data: any) => {
    return this.instance.request({
      url: `api/subscriptionsemails/${data.statustxt}`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public sendEmail = (data: any) => {
    return this.instance.request({
      url: `api/dmaemail`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      data: data.form,
      method: 'post'
    })
  }

  public autoCount = (data: any) => {
    return this.instance.request({
      url: `api/aflex/salesinvoice/` + data.id,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'post'
    })
  }

  public syncUpdateAutocount = (data: any) => {
    return this.instance.request({
      url: `api/aflex/salesinvoice/` + data.id,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'put'
    })
  }

  public initializeDebtor = (data: any) => {
    return this.instance.request({
      url: `api/aflex/initializedebtor/` + data.userid,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }

  public fixHFCDBilling = (data: any) => {
    return this.instance.request({
      url: `api/fixbillhf/${data.id}`,
      headers: {
        Authorization: 'Bearer ' + data.token
      },
      method: 'get'
    })
  }
}

const api = new API()

export const basePath = baseUrl

// user

export const forgot = api.forgot;
export const resetPasswordNow = api.resetPasswordNow;
export const register = api.register;
export const login = api.login;
export const logout = api.logout;
export const getProfile = api.getProfile;
export const updateProfile = api.updateProfile;
export const searchUser = api.searchUser;
export const getUserName = api.getUserName;
export const updateUserPlan = api.updateUserPlan;
export const generatebillspecific = api.generatebillspecific;
export const getNextSupportID = api.getNextSupportID;
export const pushSupportID = api.pushSupportID;
export const getNextSubscriptionID = api.getNextSubscriptionID;
export const pushSubscriptionID = api.pushSubscriptionID;
export const createReturnUser = api.createReturnUser;
export const getClientBills = api.getClientBills;

//ipaytest
export const getIPay256Hash = api.getIPay256Hash;

export const getUsers = (data: any) => api.getItemsFunc(userUrl, data);
export const getUser = (data: any) => api.getItemFunc(userUrl, data);
export const createUser = (data: any) => {
  if (data.form.password) {
    data.form.password = md5(data.form.password)
  }
  return api.createItemFunc(userUrl, data)
};

export const createUserByAdmin = (data: any) => {
  return api.createItemFunc(userByAdminUrl, data)
};

export const updateUser = (data: any) => api.updateUserFunc(data);
// export const deleteUser = (data: any) => api.deleteItemFunc(userUrl, data);

// upload
export const getFile = (data: any) => api.getItemFunc(fileUrl, data)
export const deleteFile = (data: any) => api.deleteItemFunc(fileUrl, data)
export const uploadFiles = (data: any) => api.createItemFunc(uploadUrl, data)

// ticket
export const getTickets = (data: any) => api.getItemsFunc(ticketUrl, data)
export const getTicket = (data: any) => api.getItemFunc(ticketUrl, data)
export const createTicket = (data: any) => api.createItemFunc(ticketUrl, data)
export const updateTicket = (data: any) => api.updateItemFunc(ticketUrl, data)
export const deleteTicket = (data: any) => api.deleteItemFunc(ticketUrl, data)
export const openTicket = (data: any) => api.patchItemFunc(ticketUrl, data)

// finance ticket
export const getFinanceTickets = (data: any) => api.getItemsFunc(financeTicketUrl, data)
export const getFinanceTicket = (data: any) => api.getItemFunc(financeTicketUrl, data)
export const createFinanceTicket = (data: any) => api.createItemFunc(financeTicketUrl, data)
export const updateFinanceTicket = (data: any) => api.updateItemFunc(financeTicketUrl, data)
export const deleteFinanceTicket = (data: any) => api.deleteItemFunc(financeTicketUrl, data)
export const openFinanceTicket = (data: any) => api.patchItemFunc(financeTicketUrl, data)

// ticket log
export const getTicketlogs = (data: any) => api.getItemsFunc(ticketlogUrl, data)
export const getTicketlog = (data: any) => api.getItemFunc(ticketlogUrl, data)
export const createTicketlog = (data: any) => api.createItemFunc(ticketlogUrl, data)
export const updateTicketlog = (data: any) => api.updateItemFunc(ticketlogUrl, data)
export const deleteTicketlog = (data: any) => api.deleteItemFunc(ticketlogUrl, data)

// ticket summary
export const getTicketSummary = (data: any) => api.getItemsFunc(ticketSummaryUrl, data)

// plan
export const getPlans = (data: any) => api.getItemsFunc(planUrl, data)
export const getPlan = (data: any) => api.getItemFunc(planUrl, data)
export const createPlan = (data: any) => api.createItemFunc(planUrl, data)
export const updatePlan = (data: any) => api.updateItemFunc(planUrl, data)
export const deletePlan = (data: any) => api.deleteItemFunc(planUrl, data)

// plan DMA
export const getPlanDMA = (data: any) => api.getItemsFunc(planDmaUrl, data)
export const getPlanDMAByID = (data: any) => api.getItemFunc(planDmaUrl, data)

// subscription
export const getSubscriptions = (data: any) => api.getItemsFunc(subscriptionUrl, data)
export const getSubscription = (data: any) => api.getItemFunc(subscriptionUrl, data)
export const createSubscription = (data: any) => api.createItemFunc(subscriptionUrl, data)
export const updateSubscription = (data: any) => api.updateItemFunc(subscriptionUrl, data)
export const deleteSubscription = (data: any) => api.deleteItemFunc(subscriptionUrl, data)

// billing
export const getBillings = (data: any) => api.getItemsFunc(billingUrl, data)
export const getBilling = (data: any) => api.getItemFunc(billingUrl, data)
export const createBilling = (data: any) => api.createItemFunc(billingUrl, data)
export const updateBilling = (data: any) => api.updateItemFunc(billingUrl, data)
export const deleteBilling = (data: any) => api.deleteItemFunc(billingUrl, data)

// prebills
export const getPrebills = (data: any) => api.getItemsFunc(prebillsUrl, data)
export const getPrebill = (data: any) => api.getItemFunc(prebillsUrl, data)
export const createPrebills = (data: any) => api.createItemFunc(prebillsUrl, data)
export const updatePrebills = (data: any) => api.updateItemFunc(prebillsUrl, data)
export const deletePrebills = (data: any) => api.deleteItemFunc(prebillsUrl, data)
export const getPrebillItems = (data: any) => api.getItemsFunc(prebillitemUrl, data)

// payment
export const getPayments = (data: any) => api.getItemsFunc(paymentUrl, data)
export const getPayment = (data: any) => api.getItemFunc(paymentUrl, data)
export const createPayment = (data: any) => api.createItemFunc(paymentUrl, data)
export const updatePayment = (data: any) => api.updateItemFunc(paymentUrl, data)
export const deletePayment = (data: any) => api.deleteItemFunc(paymentUrl, data)

// building
export const getBuildings = (data: any) => api.getItemsFunc(buildingUrl, data)
export const getBuilding = (data: any) => api.getItemFunc(buildingUrl, data)
export const createBuilding = (data: any) => api.createItemFunc(buildingUrl, data)
export const updateBuilding = (data: any) => api.updateItemFunc(buildingUrl, data)

// subscriptionGroup
export const getSubscriptionGroups = (data: any) => api.getItemsFunc(subscriptionGroupUrl, data)
export const getSubscriptionGroup = (data: any) => api.getItemFunc(subscriptionGroupUrl, data)
export const createSubscriptionGroup = (data: any) => api.createItemFunc(subscriptionGroupUrl, data)
export const updateSubscriptionGroup = (data: any) => api.updateItemFunc(subscriptionGroupUrl, data)

// report
export const getOrphanDMAUserList = (data: any) => api.getOrphanDMAUserList(data)

export const getUnbillReport = (data: any) => api.getUnbillReport(data)
export const getBillingReport = (data: any) => api.getBillingReport(data)
export const getBillingByPlanReport = (data: any) => api.getBillingByPlanReport(data)
export const getBillingWtBuildingReport = api.getBillingWtBuildingReport
export const createSubplacePayment = (data: any) => api.createSubplacePayment(data)
export const getTicketReport = (data: any) => api.getTicketReport(data)
export const getOpenTicketReport = (data: any) => api.getOpenTicketReport(data)
export const getUpcomingBillableReport = (data: any) => api.getUpcomingBillableReport(data)
export const getBillSummary = (data: any) => api.getBillSummary(data)
export const getZeroSpecialRateReport = (data: any) => api.getItemsFunc(zeroSpecialRateReportUrl, data)
export const getInvalidIcUserList = (data: any) => api.getInvalidIcUserList(data)
export const getStatementOfAccount = (data: any) => api.getStatementOfAccount(data)

export const getBillingLogs = (data: any) => api.getBillingLogs(data)
export const getSubscriptionLogs = (data: any) => api.getSubscriptionLogs(data)
export const getPrebillLogs = (data: any) => api.getPrebillLogs(data)
export const getPlanLogs = (data: any) => api.getPlanLogs(data)


export const getDashboardSummary = api.getDashboardSummary
export const getPrebillingPaymentBreakdown = api.getPrebillingPaymentBreakdown
export const getBillingsPaymentBreakdown = api.getBillingsPaymentBreakdown

export const sendBill = api.sendBill
export const enableUser = api.enableUser
export const disableUser = api.disableUser
export const disconnectUser = api.disconnectUser

export const getDMAUserCheck = api.getDMAUserCheck
export const createDMAUser = api.createDMAUser
export const updateUserDMAPlan = api.updateUserDMAPlan

export const getUserByUsername = api.getUserByUsername
export const getUserById = api.getUserById

export const getSubscriptionsEmails = api.getSubscriptionsEmails
export const sendEmail = api.sendEmail;

export const syncAutocount = (data: any) => api.autoCount(data)
export const syncUpdateAutocount = (data: any) => api.syncUpdateAutocount(data)
export const initializeAutocountDebtor = (data: any) => api.initializeDebtor(data)

export const getSubscriberCount = (data: any) => api.getSubscriberCount(data)
export const fixHFCDBilling = (data: any) => api.fixHFCDBilling(data)
export const billMigrateToCustomer = (data: any) => api.billMigrateToCustomer(data)
export const searchPaymentSubscriptions = (data: any) => api.searchPaymentSubscriptions(data)
export const getPaymentBills = (data: any) => api.getPaymentBills(data)
export const updatePaymentSubscription = (data: any) => api.updatePaymentSubscription(data)

export const recalculatePayment = (data: any) => api.recalculatePayment(data)