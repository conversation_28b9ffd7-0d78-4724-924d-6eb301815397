<template>
    <PopupModal defaultColor="blue" modalWidthPercent="90" :title="$t('users.payments')" :btnNoText="$t('c.close')"
        :btnNoFunction="cancelFunc" v-if="item">
        <div class="mb-5">
            <table class="p-4 w-full shadow">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="font-semibold w-1/4">Invoice No</th>
                        <th class="font-semibold w-1/4">Bill Date</th>
                        <th class="font-semibold w-1/2">Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(bill, index) in localBills" :key="index" class="border-b">
                        <td class="text-center py-1">{{ bill.billno }}</td>
                        <td class="text-center py-1">{{ formatDate(bill.billdate) }}</td>
                        <td class="text-center py-1 grid grid-cols-2 bg-gray-50">
                            <p>{{ bill.totalamount }}</p>
                            <div class="flex gap-4 items-center">
                                <span>Amount to be paid:</span>
                                <input type="number"
                                    v-model.number="bill.paidamount"
                                    @input="calculateTotalAmount(localBills)"
                                    placeholder="Amount to be paid"
                                    class="border px-2 py-1 rounded w-48"
                                >
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div>
            <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="dob"
                :labelTitle="$t('payments.paymentdate')">
                <DatePicker v-model="item.paymentdate" :clearable="true" defaultColor="blue"></DatePicker>
                <ErrTextCompt :errs="errs && errs.paymentdate"></ErrTextCompt>
            </FormItemCompt>
            <FormItemCompt class="w-full md:w-1/4 inline-block px-1 align-top" labelFor="amount"
                :labelTitle="$t('payments.amount')">
                <input type="number" :value="localAmount"
                    class="border-0 p-4 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full"
                    readonly>
                <ErrTextCompt :errs="errs && errs.amount"></ErrTextCompt>
            </FormItemCompt>
            <FormItemCompt class="w-full md:w-1/4 inline-block px-1 align-top" labelFor="platform"
                :labelTitle="$t('payments.platform')">
                <TextInput name="platform" v-model="item.platform"
                    :placeholder="$t('c.frontPlc') + $t('payments.platform')" defaultColor="blue"></TextInput>
                <ErrTextCompt :errs="errs && errs.platform"></ErrTextCompt>
            </FormItemCompt>
            <FormItemCompt class="w-full md:w-1/4 inline-block px-1 align-top" labelFor="platformid"
                :labelTitle="$t('payments.platformid')">
                <TextInput name="platformid" v-model="item.platformid"
                    :placeholder="$t('c.frontPlc') + $t('payments.platformid')" defaultColor="blue"></TextInput>
                <ErrTextCompt :errs="errs && errs.platformid"></ErrTextCompt>
            </FormItemCompt>
            <FormItemCompt class="my-5" labelFor="attachments" :labelTitle="$t('payments.attachments')">
                <div>
                    <ShowFiles :files="attachments" :removeFile="removeFile" />
                </div>
                <UploadInput :addFile="addFile" :basePath="basePath" :token="token">
                </UploadInput>
            </FormItemCompt>
            <FormItemCompt class="w-full inline-block px-1 align-top" labelFor="lastIP"
                :labelTitle="$t('payments.remark')">
                <TextareaInput name="remark" :rows="5" v-model="item.remark"
                    :placeholder="$t('c.frontPlc') + $t('payments.remark')" defaultColor="blue"></TextareaInput>
            </FormItemCompt>
        </div>
        <div class="my-5">
            <div class="w-full text-center mb-5 py-1 rounded cursor-pointer bg-purple-300 hover:bg-purple-500 hover:text-white"
                @click="paymentCreate">{{ $t('payments.create') }}</div>
            <div class="w-full text-center py-1 rounded cursor-pointer bg-yellow-300 hover:bg-yellow-500 hover:text-white"
                @click="cancelFunc">{{ $t('c.back') }}</div>
        </div>
    </PopupModal>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import PopupModal from '@/components/cvui/Modal.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import TextareaInput from '../../../components/cvui/form/TextareaInput.vue'
import UploadInput from '@/components/Upload3.vue'
import ShowFiles from '@/components/cvui/form/ShowFiles.vue'
import { getFile, deleteFile, basePath } from '../../../api'
import { formatDate } from '@/components/cvui/table/util2'
export default defineComponent({
    components: {
        PopupModal,
        DatePicker,
        FormItemCompt,
        ErrTextCompt,
        TextInput,
        TextareaInput,
        UploadInput,
        ShowFiles
    },
    props: {
        item: {
            type: Object,
            required: true
        },
        cancel: {
            type: Function
        },
        submitfunc: {
            type: Function
        },
        savesilentfunc: {
            type: Function
        },
        token: {
            type: String,
            required: true
        }
    },
    mounted() {
        this.loadAttachments()
        this.calculateTotalAmount(this.item.bills);
    },
    watch: {
        'item.bills': {
            handler(newBills) {
                this.calculateTotalAmount(newBills);
            },
            deep: true,
        },
    },
    methods: {
        calculateTotalAmount(bills: any) {
            this.localAmount = bills.reduce(
                (sum: any, bill: any) => sum + (parseFloat(bill.paidamount) || 0),
                0
            );
        },
        formatMoney (p: any) {
            if (p != null) {
                return p.toFixed(2)
            } else {
                return '0.00'
            }
        },
        validate() {
            let p: boolean = true
            this.errs = {
                paymentdate: undefined,
                amount: undefined
            }
            if (!this.item.amount) {
                this.errs["amount"] = this.$t('errs.invalidAmount')
                p = false
            } else {
                this.item.amount = parseFloat(this.item.amount)
                if (this.item.amount <= 0) {
                    this.errs["amount"] = this.$t('errs.invalidAmount')
                    p = false
                }
            }
            if (!this.item.paymentdate) {
                this.errs["paymentdate"] = this.$t('errs.paymentdateCannotBeEmpty')
                p = false
            }
            return p
        },
        save() {
            if (this.submitfunc) {
                this.submitfunc(this.item)
            }
        },
        addFile(p: any) {
            if (!this.item.attachments) {
                this.item.attachments = []
            }
            this.item.attachments.push(p.file.id)
            this.attachments.push(p.file)
            this.savesilent()
        },
        savesilent() {
            if (this.savesilentfunc) {
                this.savesilentfunc(this.item)
            }
        },
        removeFile(p: any) {
            let i = this.item.attachments.indexOf(p)
            if (i > -1) {
                this.item.attachments.splice(i, 1)
                this.savesilent()
            }
            let list = this.attachments.map((p: any) => p.id || p.ID)
            let j = list.indexOf(p)
            this.attachments.splice(j, 1)
            deleteFile({ id: p, token: this.token })
        },
        cancelFunc() {
            if (this.cancel) {
                this.cancel()
            }
        },
        paymentCreate() {
            this.item.bills = this.localBills.map((bill: any) => ({
                ...bill,
                paidamount: parseFloat(bill.paidamount) || 0
            }))
            if (this.validate() && this.submitfunc) {
                this.submitfunc(this.item)
            }
        },
        loadAttachments() {
            if (this.item.attachments && this.item.attachments.length > 0) {
                this.attachments = []
                for (let i = 0; i < this.item.attachments.length; i++) {
                    getFile({ token: this.token, id: this.item.attachments[i] }).then((res: any) => {
                        this.attachments.push(res['data'])
                    })
                }
            }
        }
    },
    data() {
        let errs: any = {
            paymentdate: undefined,
            amount: undefined
        }
        let attachments: any = []
        let localAmount: number = 0
        return {
            errs,
            basePath: `${basePath}api/upload`,
            attachments,
            formatDate,
            localBills: this.item.bills ? JSON.parse(JSON.stringify(this.item.bills)) : [],
            localAmount
        }
    }
})
</script>
