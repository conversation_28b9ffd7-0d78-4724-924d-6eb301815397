<template>
    <PopupModal defaultColor="blue" modalWidthPercent="90" :title="$t('users.billings')" :btnNoText="$t('c.close')"
        :btnNoFunction="cancel" v-if="item">

        <div class="border-b p-1 px-5 rounded bg-gray-100">
            <div class="inline-block text-lg "> {{ item.email }}</div>
            <div class="inline-block mx-2 text-md rounded bg-blue-300 px-2" v-if="item.name">{{ item.name }}</div>
        </div>
        <div v-if="databases == null">
            <dloading />
        </div>
        <template v-else>
            <div class="text-xs my-2 ml-10 flex justify-between items-center">
                <div class="w-full md:w-1/5 inline-block px-1">
                    <MonthPicker v-model="selectedMonth" :clearable="true" defaultColor="blue"></MonthPicker>
                </div>
                <div>
                    <button @click="reload"
                        class="h-8 px-2 bg-gray-200 hover:bg-gray-300 text-gray-900 rounded mr-2">
                        Reload
                    </button>
                    <button @click="start"
                        class="h-8 pr-2 bg-green-500 hover:bg-green-700 text-white rounded">
                        <svgicon icon="print" dclass="inline-block w-4 h-4 ml-2 mr-1" /> Generate Consolidate Invoice
                    </button>
                </div>
            </div>
            <dtable :columns="columns" :data="databases"
                columnColor="white"
                :checkboxFunc="checkboxFunc"
                :checkboxlist="selectedBulkPayment">
                <template v-slot:action="slotProps">
                    <qrcode-vue :id="`qrcode_${slotProps.item.id}`" class="hidden mb-8"
                        :value="'https://paymentapi.highfi.com.my/api/payex/' + slotProps.item.id" level="M"
                        render-as="canvas" />
                    <br />
                    <a target="_blank" :href="payexpaylink(slotProps.item.id)"
                        v-if="slotProps.index == databases.total - 1">
                        <div class="mt-5 bg-purple-500 text-white hover:bg-gray-500 rounded p-1 cursor-pointer">
                            {{ $t('c.upaylink') }}</div>
                    </a>
                </template>
            </dtable>
            <dpagination :total="databases && databases.total || 0" :page="table.page" :limit="table.limit"
                :pageChange="pageChange" defaultColor="blue" />
        </template>
    </PopupModal>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import QrcodeVue from 'qrcode.vue'
import PopupModal from '@/components/cvui/Modal.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import svgicon from '@/components/cvui/svgcollection.vue'
import dloading from '@/components/cvui/loading.vue'
import { auth2Store } from '../../../store/auth2-store'
import { billingStore } from '../../../store/billing-store'
import { imageBase64 } from '@/assets/logo_base64.js';
import moment from 'moment'
import { getSubscription, getSubscriptionGroups } from '../../../api'
import DatePicker from '../../../components/cvui/form/DatePicker.vue';
import FormItemCompt from '../../../components/cvui/form/FormItem.vue';
import MonthPicker from '../../../components/MonthPicker.vue';
import { jsPDF } from 'jspdf';

export default defineComponent({
    setup() {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        const auth2State = auth2Store.getState()
        return {
            token: computed(() => authState.token),
            authStore: authStore,
            authState: authState,
            profile: computed(() => auth2State.profile),
            billingStore: billingStore,
            billingState: billingStore.getState(),
            isDev: computed(() => auth2State.profile.scopes && auth2State.profile.scopes.includes('dev')),
        }
    },
    mounted() {
        document.addEventListener('keydown', this.handleEscKey);
        this.isSubGroup()
        this.reload()
    },
    beforeUnmount() {
        document.removeEventListener('keydown', this.handleEscKey);
    },
    components: {
        PopupModal,
        dtable,
        dpagination,
        dloading,
        svgicon,
        QrcodeVue,
        imageBase64,
        FormItemCompt,
        DatePicker,
        MonthPicker
    },
    props: {
        item: Object,
        title: String,
        cancelFunc: Function,
        submitFunc: Function
    },
    methods: {
        isSubGroup() {
            if (this.item) {
                getSubscriptionGroups({ user: this.item.id, token: this.token }).then((res: any) => {
                    if (res.total > 0) {
                        this.subGroup = true
                    }
                })
            }
        },
        isCurrentMonth(date: string) {
            return moment(date).isSame(moment(), 'month')
        },
        handleEscKey(event: KeyboardEvent) {
            if (event.key === 'Escape') {
                this.cancel()
            }
        },
        payexpaylink(id: string) {
            return `https://paymentapi.highfi.com.my/api/payex/${id}`
        },
        highlightCss(p: string) {
            if (this.subscriptionHighlight.trim().length > 0 && p.toLowerCase().indexOf(this.subscriptionHighlight.toLowerCase()) > -1) {
                return 'border-2 border-red-600'
            } else {
                return ''
            }
        },
        cancel() {
            if (this.cancelFunc) {
                this.cancelFunc()
            }
        },
        formatMoney(p: any) {
            if (p != null) {
                return p.toFixed(2)
            } else {
                return '0.00'
            }
        },
        removeDuplicate(arraylist: any) {
            return arraylist = [...new Set(arraylist)]
        },
        getSubscribtionList() {
            let subscriptionslist2 = this.subscriptions.map((p: any) => p.id)
            let subscriptions: any = []
            if (this.databases) {
                for (let i = 0; i < this.databases.data.length; i++) {
                    let data = this.databases.data[i].subscriptions
                    subscriptions = subscriptions.concat(data)
                }
                subscriptions = this.removeDuplicate(subscriptions)
                subscriptions = subscriptions.filter((p: any) => subscriptionslist2.indexOf(p) === -1)
                subscriptions.filter((k: any) => {
                    getSubscription({ token: this.token, id: k }).then((rs) => {
                        this.subscriptions.push({
                            id: k,
                            value: this.formatadd(rs.data),
                        })
                    })
                })
            }
        },
        formatadd(p: any) {
            return p && p.address ? `${p.address.block}-${p.address.level}-${p.address.unit} ${p.address.building}` : '---'
        },
        reload() {
            this.table.page = 1
            this.table.keywords = ''
            this.keywords = ''
            this.table.limit = this.limit
            !this.showBulkPayment
            // clear any previous selections when reloading
            this.selectedBulkPayment = []
            this.loadDatabase()
        },
        shortMonthFormat(p: any) {
            let r = ''
            if (p) {
                r = moment(p).format('MM/YY')
            }
            return r
        },
        formatDate(p: any) {
            let r = ''
            if (p) {
                r = moment(p).format('DD/MM/YYYY')
            }
            return r
        },
        loadDatabase() {
            if (this.item) {
                let p = { ...this.table, token: this.token, customer: this.item.id }

                if (this.selectedMonth) {
                    const selectedDate = new Date(this.selectedMonth)

                    const day = String(selectedDate.getDate()).padStart(2, '0');
                    const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
                    const year = selectedDate.getFullYear();

                    const formattedDate = `${day}-${month}-${year}`;
                    p.$billdate = formattedDate
                }

                this.billingStore.getBillings(p)
            }
        },
        pageChange(p: any) {
            this.table.page = p
            // clear selection when changing pages
            this.selectedBulkPayment = []
            this.loadDatabase()
        },
        addOnTitle(s: any) {
            let c: any = this.item
            return s ? `${s.address['building'] || 'The Grand Subang Jaya SS15'} ${s.address['block']}-${s.address['level']}-${s.address['unit']}\n${s.contact}` : `${c.contact}`
        },
        checkboxFunc(type: string, sitem: any) {
            if (!this.databases || !this.databases.data) return
            if (type === 'checkall') {
                if (this.selectedBulkPayment.length === this.databases.data.length) {
                    this.selectedBulkPayment = []
                } else {
                    // clone to avoid binding issues
                    this.selectedBulkPayment = [...this.databases.data]
                }
            } else if (type === 'checkone') {
                const idx = this.selectedBulkPayment.findIndex((k: any) => k.id === sitem.id)
                if (idx >= 0) {
                    this.selectedBulkPayment.splice(idx, 1)
                } else {
                    this.selectedBulkPayment.push(sitem)
                }
            }
        },
        async start(){
            try {
                // Reset totals before generating a new PDF
                this.totalCurrent = 0;
                this.totalBf = 0;

                // Create a new jsPDF instance
                const doc = new jsPDF();

                // Get customer name
                const customerName = this.item ? (this.item.name || this.item.email || 'Customer') : 'Customer';
                const todayDate = moment().format('DD/MM/YYYY');

                // Logo
                doc.addImage(imageBase64, 'JPEG', 8, 8, 50, 50);

                // Add company header
                doc.setFontSize(10);
                doc.text('HIIFI (M) SDN. BHD. (1406319-P)\nA1-2-7, Level 2, Block A1,\nSunway Geo Avenue,\nJalan Lagoon Selatan,\n47500 Subang Jaya',
                65, 30,
                { align: 'left' });

                // Add customer info
                doc.setFontSize(11);
                doc.text(`Customer: ${customerName}`, 110, 50);
                doc.text(`Printed Date: ${todayDate}`, 110, 55);

                // Add title
                doc.setFontSize(22);
                doc.text('Consolidate Bills', 20, 70, { align: 'left' });

                // Add table headers
                let y = 80;

                // Add horizontal line
                y += 5;
                doc.line(20, y, 190, y);
                y += 10;

                // Add bill items
                const dataSource = (this.selectedBulkPayment && this.selectedBulkPayment.length > 0)
                    ? this.selectedBulkPayment
                    : (this.databases && this.databases.data ? this.databases.data : [])

                if (dataSource.length > 0) {
                    for (let i = 0; i < dataSource.length; i++) {
                        const bill = dataSource[i];

                        // Check if we need a new page
                        if (y > 250) {
                            doc.addPage();
                            y = 20;
                        }

                        // Add bill number with better styling
                        doc.setFontSize(11);
                        doc.text(bill.billno + ' - ' + bill.sid || '', 20, y);

                        // Add address
                        let addressText = '';
                        if (bill.unit) {
                            addressText = bill.unit.trim();
                        } else if (bill.address?.address) {
                            addressText = bill.address.address.trim();
                        }
                        const maxLength = 50;
                        if (addressText.length > maxLength) {
                            addressText = addressText.substring(0, maxLength - 3) + '...';
                        }
                        doc.setFontSize(10);
                        doc.text(addressText, 20, y+4);

                        y += 8;

                        // Add BF amount if any
                        if (bill.amountbf && bill.amountbf !== 0) {
                            this.totalBf += bill.amountbf;
                        }

                        // Add bill items with better layout
                        if (bill.items && bill.items.length > 0) {
                            bill.items.forEach((item: any) => {
                                // Check if we need a new page
                                if (y > 270) {
                                    doc.addPage();
                                    y = 20;

                                    // Reprint headers on new page
                                    doc.setFontSize(12);
                                    doc.text('Bill No.', 20, y);
                                    doc.text('Amount (RM)', 170, y, { align: 'right' });
                                    y += 5;
                                    doc.line(20, y, 190, y);
                                    y += 10;
                                }

                                // Item description - aligned with invoice number
                                doc.setFontSize(10);
                                doc.text(item.itemname || 'Item', 20, y);

                                // Item amount
                                doc.text(item.amount.toFixed(2), 170, y, { align: 'right' });

                                // Move to next line
                                y += 14;
                            });
                            // Only increment total once per bill, not per item
                            bill.items.forEach((item: any) => {
                                // To avoid 2 decimal places
                                this.totalCurrent += parseFloat(item.amount.toFixed(2));
                            });
                        }

                        // Add a gap between bills
                        y += 10;

                        // Add a separator line between bills
                        doc.setDrawColor(200, 200, 200);
                        doc.line(20, y - 5, 190, y - 5);
                        doc.setDrawColor(0, 0, 0);
                    }
                } else {
                    doc.setFontSize(12);
                    doc.text('No bills found for the selected period.', 105, y, { align: 'center' });
                    y += 20;
                }
                // Check if we need a new page for totals and notes
                if (y > 200) {
                    doc.addPage();
                    y = 30;
                }

                // Add horizontal line
                doc.line(20, y, 190, y);
                y += 10;

                // Add totals
                doc.setFontSize(12);
                doc.text('Subtotal:', 120, y);
                doc.text(this.totalCurrent.toFixed(2), 170, y, { align: 'right' });
                y += 10;

                doc.text('Amount B/F:', 120, y);
                doc.text(this.totalBf.toFixed(2), 170, y, { align: 'right' });
                y += 10;

                doc.line(120, y, 190, y);
                y += 10;

                doc.setFontSize(14);
                doc.text('Total:', 120, y);
                doc.text((this.totalCurrent + this.totalBf).toFixed(2), 170, y, { align: 'right' });

                // Add some space before the footer
                y += 30;

                // Check if we need a new page for the footer
                if (y > 240) {
                    doc.addPage();
                    y = 30;
                }

                // Add footer with notes               
                y = 270;
                doc.setFontSize(9);
                // doc.text('Notes:', 20, y);
                // doc.text('1. All remittance should be made payable to HIIFI(M) SDN. BHD. (CIMB **********)', 30, y + 10);
                // doc.text('2. Please include the invoice number at the recipient reference.', 30, y + 20);
                // doc.text('3. Please share the bank in slip to HIIFI Support + 6019-719 9799', 30, y + 30);

                // Save and open the PDF
                doc.save('consolidated_invoice.pdf');

            } catch (error) {
                console.error('Error creating PDF document:', error);
                alert('Failed to generate PDF. Please try again later.');
            }
        }
    },
    computed: {
        columns() {
            let ar: any = [
                { title: 'billings.billdate', key: 'billdate', type: 'date', class: 'text-center' },
                { title: 'billings.total', key: 'totalamount', type: 'price', class: 'text-center' },
                { title: 'billings.duedate', key: 'duedate', type: 'date', class: 'text-center' },
                { title: 'billings.paid', key: 'amountpaid', type: 'price', class: 'text-center' },
            ]
            // Add bill no and a checkbox column (checkbox shown first)
            ar.unshift({ title: 'billings.billno', key: 'billno', type: 'text', class: 'text-center' })
            ar.unshift({ title: 'Select', key: 'checkbox', type: 'checkbox', class: 'text-center', checkall: true, hclass: 'w-10' })
            return ar
        },
        databases() {
            return billingStore.getState().billings
        }
    },
    watch: {
        databases(p: any) {
            if (p) {
                this.getSubscribtionList()
            }
        },
        selectedMonth(newValue, oldValue) {
            if (newValue !== oldValue) {
                this.loadDatabase();
            }
        },
    },
    data() {
        let table: any = {
            page: 1,
            keywords: ''
        }
        let paymentitem: any = undefined
        let fixviewitem: any = undefined
        let keywords: string = ''
        let showId: boolean = false
        let showTxt: any = ''
        let showSend: boolean = false
        let subscription: any = undefined
        let showUnit: boolean = false
        let showFixBill: boolean = false
        let subscriptions: any = []
        let showsublist: boolean = false
        let subscriptionHighlight: string = ''
        let startDate: any = null
        let endDate: any = null
        let showBulkPayment: any = false
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        let selectedBulkPayment: any = []
        let bulkpaymentitem: any = null
        let subGroup: any = false
        let limit: any = 50
        let billdocs: any = {}
        let totalCurrent: any = 0
        let totalBf: any = 0
        return {
            table,
            subscription,
            keywords,
            paymentitem,
            fixviewitem,
            showId,
            showTxt,
            showSend,
            showUnit,
            showFixBill,
            showsublist,
            subscriptions,
            subscriptionHighlight,
            startDate,
            endDate,
            showBulkPayment,
            selectedMonth: firstDayOfMonth,
            selectedBulkPayment,
            bulkpaymentitem,
            subGroup,
            limit,
            billdocs,
            totalCurrent,
            totalBf
        }
    }
})
</script>