<template>
  <div>
    <adminheader :title="'Bill <PERSON>gration Tool'"></adminheader>
    
    <!-- Search Section -->
    <div class="p-6 bg-white shadow-sm border-b">
      <div class="max-w-md">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Search Subscription
        </label>
        <div class="flex gap-2">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Enter subscription ID or customer name"
            class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            @keyup.enter="searchSubscriptions"
          />
          <button
            @click="searchSubscriptions"
            :disabled="loading || !searchQuery.trim()"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            Search
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <dloading></dloading>
    </div>

    <!-- Search Results -->
    <div v-else-if="searchResults.length > 0" class="p-6">
      <h3 class="text-lg font-semibold mb-4">Search Results</h3>
      <div class="space-y-4">
        <div
          v-for="subscription in searchResults"
          :key="subscription.id"
          class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
          @click="selectSubscription(subscription)"
        >
          <div class="flex justify-between items-start">
            <div>
              <h4 class="font-medium text-gray-900">{{ subscription.id || subscription._id }}</h4>
              <p class="text-sm text-gray-600">Customer: {{ subscription.customer?.name || subscription.customername || 'N/A' }}</p>
              <p class="text-sm text-gray-600">Plan: {{ subscription.plan?.name || subscription.planname || 'N/A' }}</p>
              <p class="text-sm text-gray-600">Status: {{ subscription.status || subscription.subscriptionstatus || 'N/A' }}</p>
            </div>
            <button
              class="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
            >
              View Details
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- No Results -->
    <div v-else-if="searchPerformed && searchResults.length === 0" class="p-6 text-center text-gray-500">
      No subscriptions found for "{{ searchQuery }}"
    </div>

    <!-- Selected Subscription Details -->
    <div v-if="selectedSubscription" class="p-6 border-t bg-gray-50">
      <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-xl font-semibold mb-6">Subscription Details</h3>
          
          <!-- Subscription Info -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h4 class="font-medium text-gray-900 mb-3">Subscription Information</h4>
              <div class="space-y-2 text-sm">
                <p><span class="font-medium">ID:</span> {{ selectedSubscription.id || selectedSubscription._id }}</p>
                <p><span class="font-medium">Status:</span> {{ selectedSubscription.status || selectedSubscription.subscriptionstatus || 'N/A' }}</p>
                <p><span class="font-medium">Plan:</span> {{ selectedSubscription.plan?.name || selectedSubscription.planname || 'N/A' }}</p>
                <p><span class="font-medium">Created:</span> {{ formatDate(selectedSubscription.createdAt || selectedSubscription.createdat) }}</p>
              </div>
            </div>
            
            <div>
              <h4 class="font-medium text-gray-900 mb-3">Current Customer</h4>
              <div class="space-y-2 text-sm">
                <p><span class="font-medium">Name:</span> {{ selectedSubscription.customer?.name || selectedSubscription.customername || 'N/A' }}</p>
                <p><span class="font-medium">Email:</span> {{ selectedSubscription.customer?.email || selectedSubscription.customeremail || 'N/A' }}</p>
                <p><span class="font-medium">Customer ID:</span> {{ selectedSubscription.customer?.id || selectedSubscription.customerid || selectedSubscription.customer?._id || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Customer Reassignment Section -->
          <div class="border-t pt-6 mb-8">
            <h4 class="font-medium text-gray-900 mb-4">Reassign Customer</h4>
            <p class="text-sm text-gray-600 mb-4">Update the subscription to assign it to a different customer in the payment system.</p>
            <div class="flex gap-4 items-end">
              <div class="flex-1">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Search New Customer
                </label>
                <input
                  v-model="newCustomerSearch"
                  type="text"
                  placeholder="Enter customer name or email"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  @input="searchCustomers"
                />
              </div>
              <button
                @click="reassignCustomer"
                :disabled="!selectedNewCustomer || reassigning"
                class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                {{ reassigning ? 'Reassigning...' : 'Reassign Customer' }}
              </button>
            </div>
            
            <!-- Customer Search Results -->
            <div v-if="customerSearchResults.length > 0" class="mt-4 border border-gray-200 rounded-md max-h-48 overflow-y-auto">
              <div
                v-for="customer in customerSearchResults"
                :key="customer.id"
                class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                :class="{ 'bg-blue-50': selectedNewCustomer?.id === customer.id }"
                @click="selectNewCustomer(customer)"
              >
                <div class="font-medium">{{ customer.name }}</div>
                <div class="text-sm text-gray-600">{{ customer.email }}</div>
                <div class="text-xs text-gray-500">ID: {{ customer.id }}</div>
              </div>
            </div>
            
            <div v-if="selectedNewCustomer" class="mt-4 p-3 bg-blue-50 rounded-md">
              <p class="text-sm"><span class="font-medium">Selected:</span> {{ selectedNewCustomer.name }} ({{ selectedNewCustomer.email }})</p>
            </div>
          </div>

          <!-- Bills Section -->
          <div class="border-t pt-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="font-medium text-gray-900">Bills</h4>
              <button
                @click="loadBills"
                :disabled="loadingBills"
                class="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 disabled:bg-gray-300"
              >
                {{ loadingBills ? 'Loading...' : 'Refresh Bills' }}
              </button>
            </div>
            
            <div v-if="loadingBills" class="text-center py-4">
              <dloading></dloading>
            </div>
            
            <div v-else-if="bills.length > 0" class="space-y-3">
              <div
                v-for="bill in bills"
                :key="bill.id || bill._id"
                class="border border-gray-200 rounded p-3 flex justify-between items-center"
              >
                <div>
                  <p class="font-medium">{{ bill.id || bill._id }}</p>
                  <p class="text-sm text-gray-600">Amount: {{ formatCurrency(bill.amount || bill.totalamount) }}</p>
                  <p class="text-sm text-gray-600">Date: {{ formatDate(bill.date || bill.billdate || bill.createdat) }}</p>
                  <p class="text-sm text-gray-600">Status: {{ bill.status || bill.billstatus || 'N/A' }}</p>
                </div>
                <div class="text-right">
                  <p class="text-xs text-gray-500">Customer: {{ bill.customer?.name || bill.customername || 'N/A' }}</p>
                </div>
              </div>
              
              <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <div class="flex justify-between items-center">
                  <div>
                    <h5 class="font-medium text-yellow-800">Migrate Bills to New Customer</h5>
                    <p class="text-sm text-yellow-700 mt-1">
                      This will update all bills for this subscription to the new customer.
                    </p>
                  </div>
                  <button
                    @click="migrateBills"
                    :disabled="!selectedNewCustomer || migrating"
                    class="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    {{ migrating ? 'Migrating...' : 'Migrate Bills' }}
                  </button>
                </div>
              </div>
            </div>
            
            <div v-else class="text-center py-4 text-gray-500">
              No bills found for this subscription
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success/Error Messages -->
    <div v-if="successMessage" class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-md shadow-lg">
      {{ successMessage }}
    </div>
    
    <div v-if="errorMessage" class="fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-md shadow-lg">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, inject, computed, ref, onMounted } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import dloading from '@/components/cvui/loading.vue'
import { searchUser, billMigrateToCustomer, searchPaymentSubscriptions, getPaymentBills, updatePaymentSubscription } from '../../../api'
import moment from 'moment'

export default defineComponent({
  components: {
    adminheader,
    dloading
  },
  setup() {
    const authStore: any = inject("authStore")
    const token = computed(() => authStore.getState().token)

    // Reactive data
    const searchQuery = ref('')
    const searchResults = ref([])
    const selectedSubscription = ref(null)
    const newCustomerSearch = ref('')
    const customerSearchResults = ref([])
    const selectedNewCustomer = ref(null)
    const bills = ref([])
    const loading = ref(false)
    const loadingBills = ref(false)
    const reassigning = ref(false)
    const migrating = ref(false)
    const searchPerformed = ref(false)
    const successMessage = ref('')
    const errorMessage = ref('')

    // Debounce timer for customer search
    let customerSearchTimeout: any = null

    const searchSubscriptions = async () => {
      if (!searchQuery.value.trim()) return

      loading.value = true
      searchPerformed.value = true

      try {
        const response = await searchPaymentSubscriptions({
          token: token.value,
          keywords: searchQuery.value.trim(),
          skip: 0,
          limit: 20
        })

        searchResults.value = response.data || []
      } catch (error) {
        console.error('Error searching subscriptions:', error)
        showError('Failed to search subscriptions')
        searchResults.value = []
      } finally {
        loading.value = false
      }
    }

    const selectSubscription = async (subscription: any) => {
      selectedSubscription.value = subscription
      selectedNewCustomer.value = null
      newCustomerSearch.value = ''
      customerSearchResults.value = []
      bills.value = []

      // Load bills for this subscription
      loadBills()
    }

    const loadBills = async () => {
      if (!selectedSubscription.value) return

      loadingBills.value = true

      try {
        const response = await getPaymentBills({
          token: token.value,
          subscriptionId: selectedSubscription.value.id || selectedSubscription.value._id,
          skip: 0,
          limit: 100
        })

        bills.value = response.data || []
      } catch (error) {
        console.error('Error loading bills:', error)
        showError('Failed to load bills')
        bills.value = []
      } finally {
        loadingBills.value = false
      }
    }

    const searchCustomers = () => {
      if (customerSearchTimeout) {
        clearTimeout(customerSearchTimeout)
      }

      customerSearchTimeout = setTimeout(async () => {
        const searchTerm = newCustomerSearch.value.trim()
        if (!searchTerm) {
          customerSearchResults.value = []
          return
        }

        try {
          const response = await searchUser({
            token: token.value,
            keywords: searchTerm,
            limit: 10
          })

          customerSearchResults.value = response.data || []
        } catch (error) {
          console.error('Error searching customers:', error)
          customerSearchResults.value = []
        }
      }, 300)
    }

    const selectNewCustomer = (customer: any) => {
      selectedNewCustomer.value = customer
      customerSearchResults.value = []
    }

    const reassignCustomer = async () => {
      if (!selectedSubscription.value || !selectedNewCustomer.value) return

      if (!confirm(`Are you sure you want to reassign this subscription to ${selectedNewCustomer.value.name}?`)) {
        return
      }

      reassigning.value = true

      try {
        await updatePaymentSubscription({
          token: token.value,
          subscriptionId: selectedSubscription.value.id || selectedSubscription.value._id,
          updateData: {
            customer: selectedNewCustomer.value.id || selectedNewCustomer.value.ID || selectedNewCustomer.value._id
          }
        })

        // Update local data
        selectedSubscription.value.customer = selectedNewCustomer.value
        selectedSubscription.value.customername = selectedNewCustomer.value.name
        selectedSubscription.value.customeremail = selectedNewCustomer.value.email
        selectedSubscription.value.customerid = selectedNewCustomer.value.id || selectedNewCustomer.value._id

        showSuccess('Customer reassigned successfully')
      } catch (error) {
        console.error('Error reassigning customer:', error)
        showError('Failed to reassign customer')
      } finally {
        reassigning.value = false
      }
    }

    const migrateBills = async () => {
      if (!selectedSubscription.value || !selectedNewCustomer.value) return

      if (!confirm(`Are you sure you want to migrate all bills to ${selectedNewCustomer.value.name}? This action cannot be undone.`)) {
        return
      }

      migrating.value = true

      try {
        // Call the bill migration API
        await billMigrateToCustomer({
          token: token.value,
          subscriptionId: selectedSubscription.value.id || selectedSubscription.value._id,
          customerId: selectedNewCustomer.value.id ||selectedNewCustomer.value.ID || selectedNewCustomer.value._id
        })

        showSuccess('Bills migrated successfully')

        // Reload bills to show updated data
        loadBills()
      } catch (error) {
        console.error('Error migrating bills:', error)
        showError('Failed to migrate bills')
      } finally {
        migrating.value = false
      }
    }

    const formatDate = (date: string) => {
      if (!date) return 'N/A'
      return moment(date).format('DD/MM/YYYY')
    }

    const formatCurrency = (amount: number) => {
      if (typeof amount !== 'number') return 'N/A'
      return `RM ${amount.toFixed(2)}`
    }

    const showSuccess = (message: string) => {
      successMessage.value = message
      setTimeout(() => {
        successMessage.value = ''
      }, 5000)
    }

    const showError = (message: string) => {
      errorMessage.value = message
      setTimeout(() => {
        errorMessage.value = ''
      }, 5000)
    }

    return {
      searchQuery,
      searchResults,
      selectedSubscription,
      newCustomerSearch,
      customerSearchResults,
      selectedNewCustomer,
      bills,
      loading,
      loadingBills,
      reassigning,
      migrating,
      searchPerformed,
      successMessage,
      errorMessage,
      searchSubscriptions,
      selectSubscription,
      loadBills,
      searchCustomers,
      selectNewCustomer,
      reassignCustomer,
      migrateBills,
      formatDate,
      formatCurrency
    }
  }
})
</script>
