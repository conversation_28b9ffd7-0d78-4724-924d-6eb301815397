<template>
  <div>
    <adminheader
        :title="$t('navigations.admin')"></adminheader>
    <div class="py-8 px-5 m-0 flex flex-wrap content-start items-start">
      <div class="inline-block m-2 w-32 h-24">
        <router-link :to="{name: 'importsubscribers'}" :class="btnClass('green')">
          {{$t('admin.importsubscribers')}}
        </router-link>
      </div>
      <div class="inline-block m-2 w-32 h-24">
        <router-link :to="{name: 'useremails'}" :class="btnClass('green')">
          User Email
        </router-link>
      </div>
      <div class="inline-block m-2 w-32 h-24">
        <div :class="btnClass('blue')">
          {{$t('admin.generateMissingBills')}}
        </div>
      </div>
      <div class="inline-block m-2 w-32 h-24">
        <div :class="btnClass('purple')">
          {{$t('admin.reports')}}
        </div>
      </div>
      <div v-if="isJnx" class="inline-block m-2 w-32 h-24">
        <div :class="btnClass('green')">
          {{$t('admin.jnxreport')}}
        </div>
      </div>   
      <div v-if="isJnx" class="inline-block m-2 w-32 h-24">
        <router-link :to="{name: 'paymentbulk'}" :class="btnClass('red')">
          {{$t('admin.paymentbulk')}}
        </router-link>
      </div>     
      <div v-if="isJnx" class="inline-block m-2 w-32 h-24">
        <router-link :to="{name: 'emailtools'}" :class="btnClass('red')">
          {{$t('admin.emailtools')}}
        </router-link>
      </div>     
      <div v-if="isJnx" class="inline-block m-2 w-32 h-24">
        <router-link :to="{name: 'devlog'}" :class="btnClass('gray')">
          {{$t('admin.devlog')}}
        </router-link>
      </div>
      <div v-if="isJnx" class="inline-block m-2 w-32 h-24">
        <router-link :to="{name: 'idsync'}" :class="btnClass('gray')">
          {{$t('admin.idsync')}}
        </router-link>
      </div>
      <div v-if="isJnx" class="inline-block m-2 w-32 h-24">
        <router-link :to="{name: 'billmigrate'}" :class="btnClass('orange')">
          Bill Migration
        </router-link>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
export default defineComponent({
    components: {
        adminheader,
    },
    methods: {
      btnClass (s: String ) {
        return `flex text-center items-center justify-center cursor-pointer rounded mr-2 w-full h-24 px-2 py-1 bg-${s}-400 hover:bg-${s}-600 text-white`
      }
    },
    computed: {
      isJnx () {
        return true
      }
    }
})
</script>
